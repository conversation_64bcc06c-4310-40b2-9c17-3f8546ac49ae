# TDX Event Log 完整分析

## 1. TDX Event Log 概述

TDX (Trust Domain Extensions) Event Log 是Intel TDX环境中的可信度量机制，用于记录系统启动过程中的关键事件，确保系统完整性和可信性。

### PCR到RTMR的映射关系

根据UEFI Spec 2.10 Section 38.4.1：

| TPM PCR Index | CC Measurement Register Index | TDX Measurement Register |
|---------------|-------------------------------|--------------------------|
| 0             | 0                             | MRTD                     |
| 1, 7          | 1                             | RTMR[0]                  |
| 2-6           | 2                             | RTMR[1]                  |
| 8-15          | 3                             | RTMR[2]                  |

## 2. 所有TDX Event Log类型

### 2.1 标准TCG事件类型

| 事件类型 | 值 | 描述 | 关键性 |
|----------|----|----- |--------|
| EV_PREBOOT_CERT | 0x00000000 | 预启动证书 | 🔴 高 |
| EV_POST_CODE | 0x00000001 | POST代码 | 🟡 中 |
| EV_NO_ACTION | 0x00000003 | 无操作事件 | 🟢 低 |
| EV_SEPARATOR | 0x00000004 | 分隔符事件 | 🔴 高 |
| EV_ACTION | 0x00000005 | 操作事件 | 🟡 中 |
| EV_EVENT_TAG | 0x00000006 | 事件标签 | 🟢 低 |
| EV_S_CRTM_CONTENTS | 0x00000007 | S-CRTM内容 | 🔴 高 |
| EV_S_CRTM_VERSION | 0x00000008 | S-CRTM版本 | 🔴 高 |
| EV_CPU_MICROCODE | 0x00000009 | CPU微码 | 🔴 高 |
| EV_PLATFORM_CONFIG_FLAGS | 0x0000000A | 平台配置标志 | 🟡 中 |
| EV_TABLE_OF_DEVICES | 0x0000000B | 设备表 | 🟡 中 |
| EV_COMPACT_HASH | 0x0000000C | 紧凑哈希 | 🟢 低 |
| EV_IPL | 0x0000000D | 初始程序加载 | 🔴 高 |
| EV_NONHOST_CODE | 0x0000000F | 非主机代码 | 🟡 中 |
| EV_NONHOST_CONFIG | 0x00000010 | 非主机配置 | 🟡 中 |
| EV_NONHOST_INFO | 0x00000011 | 非主机信息 | 🟢 低 |

### 2.2 EFI特定事件类型

| 事件类型 | 值 | 描述 | 关键性 |
|----------|----|----- |--------|
| EV_EFI_VARIABLE_DRIVER_CONFIG | 0x80000001 | EFI驱动配置变量 | 🔴 高 |
| EV_EFI_VARIABLE_BOOT | 0x80000002 | EFI启动变量 | 🔴 高 |
| EV_EFI_BOOT_SERVICES_APPLICATION | 0x80000003 | EFI启动服务应用 | 🟡 中 |
| EV_EFI_BOOT_SERVICES_DRIVER | 0x80000004 | EFI启动服务驱动 | 🟡 中 |
| EV_EFI_RUNTIME_SERVICES_DRIVER | 0x80000005 | EFI运行时服务驱动 | 🟡 中 |
| EV_EFI_GPT_EVENT | 0x80000006 | EFI GPT事件 | 🔴 高 |
| EV_EFI_ACTION | 0x80000007 | EFI操作事件 | 🟡 中 |
| EV_EFI_PLATFORM_FIRMWARE_BLOB | 0x80000008 | EFI平台固件块 | 🔴 高 |
| EV_EFI_HANDOFF_TABLES | 0x80000009 | EFI移交表 | 🟡 中 |
| EV_EFI_PLATFORM_FIRMWARE_BLOB2 | 0x8000000A | EFI平台固件块2 | 🔴 高 |
| EV_EFI_HANDOFF_TABLES2 | 0x8000000B | EFI移交表2 | 🟡 中 |
| EV_EFI_HCRTM_EVENT | 0x80000010 | EFI H-CRTM事件 | 🔴 高 |
| EV_EFI_VARIABLE_AUTHORITY | 0x800000E0 | EFI变量权威 | 🔴 高 |
| EV_EFI_SPDM_FIRMWARE_BLOB | 0x800000E1 | SPDM固件块 | 🟡 中 |
| EV_EFI_SPDM_FIRMWARE_CONFIG | 0x800000E2 | SPDM固件配置 | 🟡 中 |
| EV_EFI_SPDM_DEVICE_POLICY | 0x800000E3 | SPDM设备策略 | 🔴 高 |
| EV_EFI_SPDM_DEVICE_AUTHORITY | 0x800000E4 | SPDM设备权威 | 🔴 高 |

## 3. 关键度量函数和时机

### 3.1 安全启动策略度量 (MeasureSecureBootPolicy)

**时机**: 变量写入架构协议可用时
**RTMR**: RTMR[0] (PCR 7)
**事件类型**: EV_EFI_VARIABLE_DRIVER_CONFIG

**度量的变量**:
- SecureBoot
- PK (Platform Key)
- KEK (Key Exchange Key)
- db (Authorized Signature Database)
- dbx (Forbidden Signature Database)
- dbt (Timestamp Signature Database)

### 3.2 启动变量度量 (MeasureAllBootVariables)

**时机**: ReadyToBoot事件
**RTMR**: RTMR[0] (PCR 1)
**事件类型**: EV_EFI_VARIABLE_BOOT

**度量的变量**:
- BootOrder
- Boot#### (各个启动选项)

### 3.3 移交表度量 (MeasureHandoffTables)

**时机**: ReadyToBoot事件
**RTMR**: RTMR[0] (PCR 1)
**事件类型**: EV_TABLE_OF_DEVICES

**度量内容**:
- 处理器物理位置信息
- EFI配置表

### 3.4 操作事件度量 (TdMeasureAction)

**时机**: 各个启动阶段
**RTMR**: 根据事件类型
**事件类型**: EV_EFI_ACTION

**关键操作**:
- "Calling EFI Application from Boot Option"
- "Returning from EFI Application from Boot Option"
- "Exit Boot Services Invocation"
- "Exit Boot Services Returned with Success/Failure"

### 3.5 分隔符事件 (MeasureSeparatorEvent)

**时机**: 启动阶段转换
**RTMR**: 多个RTMR
**事件类型**: EV_SEPARATOR

**作用**: 分隔不同的启动阶段，防止重放攻击

## 4. 保障系统完整性的关键事件

### 4.1 🔴 最高优先级 - 系统完整性核心

1. **EV_EFI_VARIABLE_DRIVER_CONFIG (SecureBoot相关)**
   - 确保安全启动配置的完整性
   - 防止恶意修改安全策略
   - 建立可信启动链的基础

2. **EV_SEPARATOR**
   - 分隔不同启动阶段
   - 防止事件重放攻击
   - 确保度量顺序的完整性

3. **EV_IPL (内核命令行)**
   - 度量操作系统加载参数
   - 确保启动配置的完整性
   - 防止恶意内核参数注入

4. **EV_EFI_PLATFORM_FIRMWARE_BLOB**
   - 度量固件代码
   - 确保固件完整性
   - 检测固件篡改

### 4.2 🟡 高优先级 - 启动流程保护

5. **EV_EFI_VARIABLE_BOOT**
   - 度量启动选项配置
   - 防止启动顺序篡改
   - 确保启动路径可信

6. **EV_EFI_ACTION**
   - 记录关键启动操作
   - 跟踪启动流程状态
   - 检测异常启动行为

7. **EV_TABLE_OF_DEVICES**
   - 度量硬件配置
   - 检测硬件变更
   - 确保平台一致性

### 4.3 🟢 中等优先级 - 运行时保护

8. **EV_EFI_BOOT_SERVICES_APPLICATION/DRIVER**
   - 度量加载的应用和驱动
   - 确保代码完整性
   - 检测恶意软件注入

## 5. TDX特有的安全增强

### 5.1 硬件级保护
- 基于Intel TDX硬件特性
- 内存加密和隔离
- 不可篡改的度量寄存器

### 5.2 远程证明支持
- 提供可信的启动证据
- 支持第三方验证
- 建立端到端信任链

### 5.3 实时度量
- 启动过程中实时记录
- 无法事后篡改
- 提供完整的审计轨迹

## 6. 事件度量时序分析

### 6.1 启动阶段事件序列

```
1. SEC/PEI阶段:
   - EV_S_CRTM_CONTENTS (MRTD)
   - EV_S_CRTM_VERSION (MRTD)
   - EV_PLATFORM_FIRMWARE_BLOB (MRTD)

2. DXE阶段:
   - EV_EFI_VARIABLE_DRIVER_CONFIG (RTMR[0]) - 安全启动策略
   - EV_SEPARATOR (RTMR[0]) - 阶段分隔

3. BDS阶段:
   - EV_EFI_VARIABLE_BOOT (RTMR[0]) - 启动变量
   - EV_TABLE_OF_DEVICES (RTMR[0]) - 设备表
   - EV_EFI_ACTION (RTMR[1]) - "Calling EFI Application"

4. OS加载阶段:
   - EV_IPL (RTMR[2]) - 内核命令行
   - EV_EFI_BOOT_SERVICES_APPLICATION (RTMR[1]) - OS加载器
   - EV_SEPARATOR (RTMR[1]) - 启动完成分隔

5. 退出启动服务:
   - EV_EFI_ACTION (RTMR[2]) - "Exit Boot Services"
```

### 6.2 关键度量点分析

#### 安全启动完整性链
```
SecureBoot变量 → PK → KEK → db/dbx → 代码签名验证 → 可信启动
```

#### 启动路径完整性链
```
BootOrder → Boot#### → 启动设备 → OS加载器 → 内核参数 → OS启动
```

## 7. 威胁模型和防护

### 7.1 防护的攻击类型

1. **固件篡改攻击**
   - 防护: EV_EFI_PLATFORM_FIRMWARE_BLOB
   - 检测: 固件哈希值变化

2. **启动配置攻击**
   - 防护: EV_EFI_VARIABLE_DRIVER_CONFIG, EV_EFI_VARIABLE_BOOT
   - 检测: 安全启动策略或启动顺序异常

3. **内核参数注入**
   - 防护: EV_IPL
   - 检测: 内核命令行参数异常

4. **恶意驱动注入**
   - 防护: EV_EFI_BOOT_SERVICES_DRIVER
   - 检测: 未授权驱动加载

5. **重放攻击**
   - 防护: EV_SEPARATOR
   - 检测: 事件序列异常

### 7.2 检测指标

- **完整性指标**: 关键组件哈希值
- **配置指标**: 安全启动状态、启动顺序
- **行为指标**: 启动时序、事件序列
- **环境指标**: 硬件配置、设备状态

## 8. 实际应用示例

### 8.1 示例1: SecureBoot变量度量
```
Event Type: EV_EFI_VARIABLE_DRIVER_CONFIG (0x80000001)
RTMR: 0 (PCR 7)
Data: UEFI_VARIABLE_DATA结构
- VariableName: gEfiGlobalVariableGuid
- UnicodeName: "SecureBoot"
- VariableData: 0 (禁用) 或 1 (启用)
```

### 8.2 示例2: 内核命令行度量
```
Event Type: EV_IPL (0x0D)
RTMR: 2 (PCR 5)
Data: "kernel_cmdline: /vmlinuz-6.11.0-26-generic root=UUID=... ro console=tty1"
```

## 9. 安全建议

1. **监控关键事件**: 重点关注🔴标记的高优先级事件
2. **验证度量链**: 确保所有关键组件都被正确度量
3. **检测异常**: 监控意外的事件类型或顺序
4. **远程证明**: 利用TDX的远程证明能力验证系统状态
5. **日志分析**: 定期分析事件日志，检测潜在威胁
6. **基线建立**: 建立正常启动的事件基线
7. **实时监控**: 实施实时的事件日志监控
8. **响应机制**: 建立异常事件的响应流程
